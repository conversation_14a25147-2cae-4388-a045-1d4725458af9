<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闽南语学习 - 常用口语</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .phrase-item {
            transition: all 0.3s ease;
        }
        .phrase-item.expanded {
            background: #f0fdf4;
            border-color: #10b981;
        }
        .phrase-details {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .phrase-details.show {
            max-height: 200px;
        }
        .status-bar {
            height: 44px;
            background: #000;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar"></div>
    
    <!-- 顶部导航 -->
    <div class="gradient-bg text-white px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <button class="mr-4">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <div>
                    <h1 class="text-xl font-bold">常用口语</h1>
                    <p class="text-green-100 text-sm">20个常用表达</p>
                </div>
            </div>
            <div class="flex space-x-3">
                <button class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-search text-white"></i>
                </button>
                <button class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-filter text-white"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 分类统计 -->
    <div class="px-6 py-4 bg-white border-b border-gray-100">
        <div class="flex justify-between items-center">
            <div class="flex space-x-6">
                <div class="text-center">
                    <div class="text-lg font-bold text-gray-800">20</div>
                    <div class="text-xs text-gray-500">总数</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-green-600">5</div>
                    <div class="text-xs text-gray-500">已收藏</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-blue-600">12</div>
                    <div class="text-xs text-gray-500">已学习</div>
                </div>
            </div>
            <button class="text-green-600 text-sm font-medium">全部播放</button>
        </div>
    </div>

    <!-- 口语列表 -->
    <div class="px-6 py-4">
        <div class="space-y-3">
            <!-- 口语项目 1 -->
            <div class="phrase-item bg-white rounded-xl border border-gray-100 overflow-hidden" onclick="togglePhrase(this)">
                <div class="p-4 flex items-center justify-between cursor-pointer">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-800 mb-1">你好</h3>
                        <p class="text-gray-500 text-sm">基础问候语</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="favorite-btn w-8 h-8 rounded-full flex items-center justify-center" onclick="event.stopPropagation(); toggleFavorite(this)">
                            <i class="fas fa-heart text-red-500"></i>
                        </button>
                        <i class="fas fa-chevron-down text-gray-400 expand-icon transition-transform"></i>
                    </div>
                </div>
                <div class="phrase-details px-4 pb-4">
                    <div class="bg-green-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-800 font-medium text-lg">Lí hó</span>
                            <button class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                        <div class="text-green-600 text-sm">发音：[li ho]</div>
                    </div>
                    <div class="text-gray-600 text-sm">
                        <strong>使用场景：</strong>日常见面打招呼时使用
                    </div>
                </div>
            </div>

            <!-- 口语项目 2 -->
            <div class="phrase-item bg-white rounded-xl border border-gray-100 overflow-hidden" onclick="togglePhrase(this)">
                <div class="p-4 flex items-center justify-between cursor-pointer">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-800 mb-1">谢谢</h3>
                        <p class="text-gray-500 text-sm">表达感谢</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="favorite-btn w-8 h-8 rounded-full flex items-center justify-center" onclick="event.stopPropagation(); toggleFavorite(this)">
                            <i class="far fa-heart text-gray-400"></i>
                        </button>
                        <i class="fas fa-chevron-down text-gray-400 expand-icon transition-transform"></i>
                    </div>
                </div>
                <div class="phrase-details px-4 pb-4">
                    <div class="bg-green-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-800 font-medium text-lg">To-siā</span>
                            <button class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                        <div class="text-green-600 text-sm">发音：[to sia]</div>
                    </div>
                    <div class="text-gray-600 text-sm">
                        <strong>使用场景：</strong>接受帮助或礼物时表达感谢
                    </div>
                </div>
            </div>

            <!-- 口语项目 3 -->
            <div class="phrase-item bg-white rounded-xl border border-gray-100 overflow-hidden" onclick="togglePhrase(this)">
                <div class="p-4 flex items-center justify-between cursor-pointer">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-800 mb-1">对不起</h3>
                        <p class="text-gray-500 text-sm">道歉用语</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="favorite-btn w-8 h-8 rounded-full flex items-center justify-center" onclick="event.stopPropagation(); toggleFavorite(this)">
                            <i class="fas fa-heart text-red-500"></i>
                        </button>
                        <i class="fas fa-chevron-down text-gray-400 expand-icon transition-transform"></i>
                    </div>
                </div>
                <div class="phrase-details px-4 pb-4">
                    <div class="bg-green-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-800 font-medium text-lg">Pháiⁿ-sè</span>
                            <button class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                        <div class="text-green-600 text-sm">发音：[phain se]</div>
                    </div>
                    <div class="text-gray-600 text-sm">
                        <strong>使用场景：</strong>犯错或给别人造成不便时道歉
                    </div>
                </div>
            </div>

            <!-- 口语项目 4 -->
            <div class="phrase-item bg-white rounded-xl border border-gray-100 overflow-hidden" onclick="togglePhrase(this)">
                <div class="p-4 flex items-center justify-between cursor-pointer">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-800 mb-1">再见</h3>
                        <p class="text-gray-500 text-sm">告别用语</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="favorite-btn w-8 h-8 rounded-full flex items-center justify-center" onclick="event.stopPropagation(); toggleFavorite(this)">
                            <i class="far fa-heart text-gray-400"></i>
                        </button>
                        <i class="fas fa-chevron-down text-gray-400 expand-icon transition-transform"></i>
                    </div>
                </div>
                <div class="phrase-details px-4 pb-4">
                    <div class="bg-green-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-800 font-medium text-lg">Chài-kìⁿ</span>
                            <button class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                        <div class="text-green-600 text-sm">发音：[chai kinn]</div>
                    </div>
                    <div class="text-gray-600 text-sm">
                        <strong>使用场景：</strong>分别时的告别用语
                    </div>
                </div>
            </div>

            <!-- 口语项目 5 -->
            <div class="phrase-item bg-white rounded-xl border border-gray-100 overflow-hidden" onclick="togglePhrase(this)">
                <div class="p-4 flex items-center justify-between cursor-pointer">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-800 mb-1">请问</h3>
                        <p class="text-gray-500 text-sm">礼貌询问</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="favorite-btn w-8 h-8 rounded-full flex items-center justify-center" onclick="event.stopPropagation(); toggleFavorite(this)">
                            <i class="far fa-heart text-gray-400"></i>
                        </button>
                        <i class="fas fa-chevron-down text-gray-400 expand-icon transition-transform"></i>
                    </div>
                </div>
                <div class="phrase-details px-4 pb-4">
                    <div class="bg-green-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-800 font-medium text-lg">Chhiáⁿ-mn̄g</span>
                            <button class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                        <div class="text-green-600 text-sm">发音：[chhiann mng]</div>
                    </div>
                    <div class="text-gray-600 text-sm">
                        <strong>使用场景：</strong>向他人询问问题时的礼貌用语
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-3">
        <div class="flex justify-around">
            <div class="flex flex-col items-center">
                <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">首页</span>
            </div>
            <div class="flex flex-col items-center">
                <i class="fas fa-heart text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">收藏</span>
            </div>
            <div class="flex flex-col items-center">
                <i class="fas fa-cog text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">设置</span>
            </div>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="h-20"></div>

    <script>
        function togglePhrase(element) {
            const details = element.querySelector('.phrase-details');
            const icon = element.querySelector('.expand-icon');
            
            if (details.classList.contains('show')) {
                details.classList.remove('show');
                icon.style.transform = 'rotate(0deg)';
                element.classList.remove('expanded');
            } else {
                details.classList.add('show');
                icon.style.transform = 'rotate(180deg)';
                element.classList.add('expanded');
            }
        }

        function toggleFavorite(button) {
            const icon = button.querySelector('i');
            if (icon.classList.contains('far')) {
                icon.classList.remove('far', 'text-gray-400');
                icon.classList.add('fas', 'text-red-500');
            } else {
                icon.classList.remove('fas', 'text-red-500');
                icon.classList.add('far', 'text-gray-400');
            }
        }
    </script>
</body>
</html>
