<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闽南语学习 - 我的收藏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .phrase-item {
            transition: all 0.3s ease;
        }
        .phrase-item.expanded {
            background: #f0fdf4;
            border-color: #10b981;
        }
        .phrase-details {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .phrase-details.show {
            max-height: 200px;
        }
        .status-bar {
            height: 44px;
            background: #000;
        }
        .empty-state {
            opacity: 0.6;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar"></div>
    
    <!-- 顶部导航 -->
    <div class="gradient-bg text-white px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div>
                    <h1 class="text-xl font-bold">我的收藏</h1>
                    <p class="text-green-100 text-sm">5个收藏的表达</p>
                </div>
            </div>
            <div class="flex space-x-3">
                <button class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-search text-white"></i>
                </button>
                <button class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-sort text-white"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 收藏统计 -->
    <div class="px-6 py-4 bg-white border-b border-gray-100">
        <div class="flex justify-between items-center">
            <div class="flex space-x-6">
                <div class="text-center">
                    <div class="text-lg font-bold text-red-500">5</div>
                    <div class="text-xs text-gray-500">总收藏</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-green-600">3</div>
                    <div class="text-xs text-gray-500">常用</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-blue-600">2</div>
                    <div class="text-xs text-gray-500">问候</div>
                </div>
            </div>
            <button class="text-green-600 text-sm font-medium">批量管理</button>
        </div>
    </div>

    <!-- 快速筛选 -->
    <div class="px-6 py-3 bg-white border-b border-gray-100">
        <div class="flex space-x-2">
            <button class="px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm font-medium">全部</button>
            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">常用</button>
            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">问候</button>
            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">用餐</button>
        </div>
    </div>

    <!-- 收藏列表 -->
    <div class="px-6 py-4">
        <div class="space-y-3">
            <!-- 收藏项目 1 -->
            <div class="phrase-item bg-white rounded-xl border border-gray-100 overflow-hidden" onclick="togglePhrase(this)">
                <div class="p-4 flex items-center justify-between cursor-pointer">
                    <div class="flex-1">
                        <div class="flex items-center mb-1">
                            <h3 class="font-medium text-gray-800 mr-2">你好</h3>
                            <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full">常用</span>
                        </div>
                        <p class="text-gray-500 text-sm">基础问候语</p>
                        <div class="text-xs text-gray-400 mt-1">收藏于 2024-01-15</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="favorite-btn w-8 h-8 rounded-full flex items-center justify-center" onclick="event.stopPropagation(); removeFavorite(this)">
                            <i class="fas fa-heart text-red-500"></i>
                        </button>
                        <i class="fas fa-chevron-down text-gray-400 expand-icon transition-transform"></i>
                    </div>
                </div>
                <div class="phrase-details px-4 pb-4">
                    <div class="bg-green-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-800 font-medium text-lg">Lí hó</span>
                            <button class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                        <div class="text-green-600 text-sm">发音：[li ho]</div>
                    </div>
                    <div class="text-gray-600 text-sm">
                        <strong>使用场景：</strong>日常见面打招呼时使用
                    </div>
                </div>
            </div>

            <!-- 收藏项目 2 -->
            <div class="phrase-item bg-white rounded-xl border border-gray-100 overflow-hidden" onclick="togglePhrase(this)">
                <div class="p-4 flex items-center justify-between cursor-pointer">
                    <div class="flex-1">
                        <div class="flex items-center mb-1">
                            <h3 class="font-medium text-gray-800 mr-2">对不起</h3>
                            <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full">常用</span>
                        </div>
                        <p class="text-gray-500 text-sm">道歉用语</p>
                        <div class="text-xs text-gray-400 mt-1">收藏于 2024-01-14</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="favorite-btn w-8 h-8 rounded-full flex items-center justify-center" onclick="event.stopPropagation(); removeFavorite(this)">
                            <i class="fas fa-heart text-red-500"></i>
                        </button>
                        <i class="fas fa-chevron-down text-gray-400 expand-icon transition-transform"></i>
                    </div>
                </div>
                <div class="phrase-details px-4 pb-4">
                    <div class="bg-green-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-800 font-medium text-lg">Pháiⁿ-sè</span>
                            <button class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                        <div class="text-green-600 text-sm">发音：[phain se]</div>
                    </div>
                    <div class="text-gray-600 text-sm">
                        <strong>使用场景：</strong>犯错或给别人造成不便时道歉
                    </div>
                </div>
            </div>

            <!-- 收藏项目 3 -->
            <div class="phrase-item bg-white rounded-xl border border-gray-100 overflow-hidden" onclick="togglePhrase(this)">
                <div class="p-4 flex items-center justify-between cursor-pointer">
                    <div class="flex-1">
                        <div class="flex items-center mb-1">
                            <h3 class="font-medium text-gray-800 mr-2">早安</h3>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded-full">问候</span>
                        </div>
                        <p class="text-gray-500 text-sm">早晨问候</p>
                        <div class="text-xs text-gray-400 mt-1">收藏于 2024-01-13</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="favorite-btn w-8 h-8 rounded-full flex items-center justify-center" onclick="event.stopPropagation(); removeFavorite(this)">
                            <i class="fas fa-heart text-red-500"></i>
                        </button>
                        <i class="fas fa-chevron-down text-gray-400 expand-icon transition-transform"></i>
                    </div>
                </div>
                <div class="phrase-details px-4 pb-4">
                    <div class="bg-green-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-800 font-medium text-lg">Chá-khí-chá</span>
                            <button class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                        <div class="text-green-600 text-sm">发音：[cha khi cha]</div>
                    </div>
                    <div class="text-gray-600 text-sm">
                        <strong>使用场景：</strong>早晨见面时的问候语
                    </div>
                </div>
            </div>

            <!-- 收藏项目 4 -->
            <div class="phrase-item bg-white rounded-xl border border-gray-100 overflow-hidden" onclick="togglePhrase(this)">
                <div class="p-4 flex items-center justify-between cursor-pointer">
                    <div class="flex-1">
                        <div class="flex items-center mb-1">
                            <h3 class="font-medium text-gray-800 mr-2">晚安</h3>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded-full">问候</span>
                        </div>
                        <p class="text-gray-500 text-sm">晚间告别</p>
                        <div class="text-xs text-gray-400 mt-1">收藏于 2024-01-12</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="favorite-btn w-8 h-8 rounded-full flex items-center justify-center" onclick="event.stopPropagation(); removeFavorite(this)">
                            <i class="fas fa-heart text-red-500"></i>
                        </button>
                        <i class="fas fa-chevron-down text-gray-400 expand-icon transition-transform"></i>
                    </div>
                </div>
                <div class="phrase-details px-4 pb-4">
                    <div class="bg-green-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-800 font-medium text-lg">Àm-mî hó</span>
                            <button class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                        <div class="text-green-600 text-sm">发音：[am mi ho]</div>
                    </div>
                    <div class="text-gray-600 text-sm">
                        <strong>使用场景：</strong>晚上分别时的告别语
                    </div>
                </div>
            </div>

            <!-- 收藏项目 5 -->
            <div class="phrase-item bg-white rounded-xl border border-gray-100 overflow-hidden" onclick="togglePhrase(this)">
                <div class="p-4 flex items-center justify-between cursor-pointer">
                    <div class="flex-1">
                        <div class="flex items-center mb-1">
                            <h3 class="font-medium text-gray-800 mr-2">不客气</h3>
                            <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full">常用</span>
                        </div>
                        <p class="text-gray-500 text-sm">回应感谢</p>
                        <div class="text-xs text-gray-400 mt-1">收藏于 2024-01-11</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="favorite-btn w-8 h-8 rounded-full flex items-center justify-center" onclick="event.stopPropagation(); removeFavorite(this)">
                            <i class="fas fa-heart text-red-500"></i>
                        </button>
                        <i class="fas fa-chevron-down text-gray-400 expand-icon transition-transform"></i>
                    </div>
                </div>
                <div class="phrase-details px-4 pb-4">
                    <div class="bg-green-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-800 font-medium text-lg">M̄-bián kheh-khì</span>
                            <button class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                        <div class="text-green-600 text-sm">发音：[m bian kheh khi]</div>
                    </div>
                    <div class="text-gray-600 text-sm">
                        <strong>使用场景：</strong>别人向你道谢时的回应
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-3">
        <div class="flex justify-around">
            <div class="flex flex-col items-center">
                <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">首页</span>
            </div>
            <div class="flex flex-col items-center">
                <i class="fas fa-heart text-red-500 text-lg mb-1"></i>
                <span class="text-xs text-red-500 font-medium">收藏</span>
            </div>
            <div class="flex flex-col items-center">
                <i class="fas fa-cog text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">设置</span>
            </div>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="h-20"></div>

    <script>
        function togglePhrase(element) {
            const details = element.querySelector('.phrase-details');
            const icon = element.querySelector('.expand-icon');
            
            if (details.classList.contains('show')) {
                details.classList.remove('show');
                icon.style.transform = 'rotate(0deg)';
                element.classList.remove('expanded');
            } else {
                details.classList.add('show');
                icon.style.transform = 'rotate(180deg)';
                element.classList.add('expanded');
            }
        }

        function removeFavorite(button) {
            const phraseItem = button.closest('.phrase-item');
            phraseItem.style.opacity = '0.5';
            phraseItem.style.transform = 'translateX(-20px)';
            
            setTimeout(() => {
                phraseItem.remove();
                // 更新收藏数量
                updateFavoriteCount();
            }, 300);
        }

        function updateFavoriteCount() {
            const remainingItems = document.querySelectorAll('.phrase-item').length - 1;
            document.querySelector('.gradient-bg p').textContent = `${remainingItems}个收藏的表达`;
            document.querySelector('.text-red-500').textContent = remainingItems;
        }
    </script>
</body>
</html>
