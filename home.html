<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闽南语学习 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f0fdfa 0%, #ecfdf5 25%, #ffffff 50%, #f0f9ff 75%, #ffffff 100%);
            min-height: 100vh;
        }
        .status-bar {
            height: 44px;
            background: #000;
        }
        .green-primary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .nav-item {
            position: relative;
        }
        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 100%;
            height: 4px;
            background-color: white;
        }
        .category-card {
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .category-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 35px rgba(16, 185, 129, 0.15);
            background: rgba(255, 255, 255, 0.95);
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            border-left: 4px solid #10b981;
            padding-left: 12px;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            padding: 12px;
            margin: 0 -4px 16px -4px;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #065f46;
        }
        .see-all {
            font-size: 14px;
            color: #10b981;
            font-weight: 500;
        }
        .bottom-nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(16, 185, 129, 0.1);
        }
        .nav-active {
            color: #10b981 !important;
        }
        .nav-inactive {
            color: #6b7280 !important;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar"></div>
    


    <!-- 词汇分类 -->
    <div class="px-4 py-4">
        <div class="section-header">
            <span class="section-title">词汇</span>
            <a href="#" class="see-all">所有 >></a>
        </div>
        
        <div class="grid grid-cols-3 gap-3">
            <!-- 精选必备60词 -->
            <div class="category-card bg-blue-100 rounded-xl p-4 flex flex-col items-center justify-center aspect-square">
                <div class="text-4xl text-blue-500 mb-2">Aa</div>
                <div class="text-center text-sm font-medium">精选必备60词</div>
            </div>
            
            <!-- 常用基础词汇 -->
            <div class="category-card bg-green-100 rounded-xl p-4 flex flex-col items-center justify-center aspect-square">
                <div class="text-4xl text-green-500 mb-2">Bb</div>
                <div class="text-center text-sm font-medium">常用基础词汇</div>
            </div>
            
            <!-- 旅游必备词句 -->
            <div class="category-card bg-orange-100 rounded-xl p-4 flex flex-col items-center justify-center aspect-square">
                <div class="text-4xl text-orange-500 mb-2"><i class="fas fa-mountain"></i></div>
                <div class="text-center text-sm font-medium">旅游必备词句</div>
            </div>
        </div>
    </div>

    <!-- 对话分类 -->
    <div class="px-4 py-4">
        <div class="section-header">
            <span class="section-title">对话</span>
            <a href="#" class="see-all">所有 >></a>
        </div>
        
        <div class="grid grid-cols-3 gap-3 mb-4">
            <!-- 日常对话 -->
            <div class="category-card bg-teal-100 rounded-xl p-4 flex flex-col items-center justify-center aspect-square">
                <div class="text-3xl text-teal-500 mb-2"><i class="fas fa-mug-hot"></i></div>
                <div class="text-center text-sm font-medium">日常对话</div>
            </div>
            
            <!-- 交际应酬 -->
            <div class="category-card bg-pink-100 rounded-xl p-4 flex flex-col items-center justify-center aspect-square">
                <div class="text-3xl text-pink-500 mb-2"><i class="fas fa-comments"></i></div>
                <div class="text-center text-sm font-medium">交际应酬</div>
            </div>
            
            <!-- 生活杂事 -->
            <div class="category-card bg-amber-100 rounded-xl p-4 flex flex-col items-center justify-center aspect-square">
                <div class="text-3xl text-amber-500 mb-2"><i class="fas fa-gamepad"></i></div>
                <div class="text-center text-sm font-medium">生活杂事</div>
            </div>
        </div>
        
        <div class="grid grid-cols-3 gap-3">
            <!-- 购物 -->
            <div class="category-card bg-purple-100 rounded-xl p-4 flex flex-col items-center justify-center aspect-square">
                <div class="text-3xl text-purple-500 mb-2"><i class="fas fa-shopping-bag"></i></div>
                <div class="text-center text-sm font-medium">购物</div>
            </div>
            
            <!-- 问路 -->
            <div class="category-card bg-blue-100 rounded-xl p-4 flex flex-col items-center justify-center aspect-square">
                <div class="text-3xl text-blue-500 mb-2"><i class="fas fa-map-marker-alt"></i></div>
                <div class="text-center text-sm font-medium">问路</div>
            </div>
            
            <!-- 餐厅用语 -->
            <div class="category-card bg-rose-100 rounded-xl p-4 flex flex-col items-center justify-center aspect-square">
                <div class="text-3xl text-rose-500 mb-2"><i class="fas fa-utensils"></i></div>
                <div class="text-center text-sm font-medium">餐厅用语</div>
            </div>
        </div>
    </div>



    <!-- 更多方言APP -->
    <div class="px-4 py-4">
        <div class="section-header">
            <span class="section-title">更多</span>
        </div>
        
        <div class="flex space-x-4 overflow-x-auto pb-2">
            <!-- 潮汕话学习 -->
            <div class="flex-shrink-0 category-card bg-white rounded-xl p-4 shadow-sm border border-gray-100 w-40">
                <div class="mb-2 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-language text-red-600 text-xl"></i>
                </div>
                <h3 class="font-medium text-gray-800 mb-1">潮汕话学习</h3>
                <p class="text-gray-500 text-xs">学习潮汕方言</p>
            </div>
            
            <!-- 其他方言APP位置预留 -->
            <div class="flex-shrink-0 category-card bg-white rounded-xl p-4 shadow-sm border border-gray-100 w-40">
                <div class="mb-2 w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-language text-purple-600 text-xl"></i>
                </div>
                <h3 class="font-medium text-gray-800 mb-1">客家话学习</h3>
                <p class="text-gray-500 text-xs">学习客家方言</p>
            </div>
            
            <div class="flex-shrink-0 category-card bg-white rounded-xl p-4 shadow-sm border border-gray-100 w-40">
                <div class="mb-2 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-language text-green-600 text-xl"></i>
                </div>
                <h3 class="font-medium text-gray-800 mb-1">粤语学习</h3>
                <p class="text-gray-500 text-xs">学习广东话</p>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex justify-around py-2">
            <div class="flex flex-col items-center">
                <i class="fas fa-home text-red-600 text-xl"></i>
                <span class="text-xs text-red-600">首页</span>
            </div>
            <div class="flex flex-col items-center">
                <i class="fas fa-search text-gray-400 text-xl"></i>
                <span class="text-xs text-gray-400">搜索</span>
            </div>
            <div class="flex flex-col items-center">
                <i class="fas fa-heart text-gray-400 text-xl"></i>
                <span class="text-xs text-gray-400">收藏</span>
            </div>
            <div class="flex flex-col items-center">
                <i class="fas fa-cog text-gray-400 text-xl"></i>
                <span class="text-xs text-gray-400">设置</span>
            </div>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="h-20"></div>
</body>
</html>
