<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闽南语学习 - 设置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .setting-item {
            transition: all 0.2s ease;
        }
        .setting-item:active {
            background-color: #f9fafb;
        }
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background-color: #d1d5db;
            border-radius: 12px;
            transition: background-color 0.3s ease;
            cursor: pointer;
        }
        .toggle-switch.active {
            background-color: #10b981;
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
        .status-bar {
            height: 44px;
            background: #000;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar"></div>
    
    <!-- 顶部导航 -->
    <div class="gradient-bg text-white px-6 py-4">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-xl font-bold">设置</h1>
                <p class="text-green-100 text-sm">个人偏好设置</p>
            </div>
            <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <i class="fas fa-cog text-white"></i>
            </div>
        </div>
    </div>

    <!-- 用户信息 -->
    <div class="px-6 py-6 bg-white border-b border-gray-100">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mr-4">
                <span class="text-white text-xl font-bold">学</span>
            </div>
            <div class="flex-1">
                <h3 class="font-semibold text-gray-800 text-lg">学习者</h3>
                <p class="text-gray-500 text-sm">已学习 15 天</p>
                <div class="flex items-center mt-1">
                    <div class="w-20 h-2 bg-gray-200 rounded-full mr-2">
                        <div class="w-3/5 h-full bg-green-500 rounded-full"></div>
                    </div>
                    <span class="text-xs text-gray-400">初级</span>
                </div>
            </div>
            <i class="fas fa-chevron-right text-gray-300"></i>
        </div>
    </div>

    <!-- 学习设置 -->
    <div class="px-6 py-4">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">学习设置</h2>
        <div class="bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-100">
            <!-- 语音设置 -->
            <div class="setting-item px-4 py-4 flex items-center justify-between border-b border-gray-100">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-volume-up text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">语音播放</h3>
                        <p class="text-gray-500 text-sm">自动播放发音</p>
                    </div>
                </div>
                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
            </div>

            <!-- 学习提醒 -->
            <div class="setting-item px-4 py-4 flex items-center justify-between border-b border-gray-100">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-bell text-orange-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">学习提醒</h3>
                        <p class="text-gray-500 text-sm">每日学习提醒</p>
                    </div>
                </div>
                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
            </div>

            <!-- 夜间模式 -->
            <div class="setting-item px-4 py-4 flex items-center justify-between border-b border-gray-100">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-moon text-purple-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">夜间模式</h3>
                        <p class="text-gray-500 text-sm">护眼深色主题</p>
                    </div>
                </div>
                <div class="toggle-switch" onclick="toggleSwitch(this)"></div>
            </div>

            <!-- 学习目标 -->
            <div class="setting-item px-4 py-4 flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-target text-green-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">每日目标</h3>
                        <p class="text-gray-500 text-sm">学习 10 个新词汇</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
        </div>
    </div>

    <!-- 应用设置 -->
    <div class="px-6 py-4">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">应用设置</h2>
        <div class="bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-100">
            <!-- 语言设置 -->
            <div class="setting-item px-4 py-4 flex items-center justify-between border-b border-gray-100">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-language text-red-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">界面语言</h3>
                        <p class="text-gray-500 text-sm">简体中文</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>

            <!-- 字体大小 -->
            <div class="setting-item px-4 py-4 flex items-center justify-between border-b border-gray-100">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-font text-indigo-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">字体大小</h3>
                        <p class="text-gray-500 text-sm">标准</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>

            <!-- 缓存管理 -->
            <div class="setting-item px-4 py-4 flex items-center justify-between border-b border-gray-100">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-database text-gray-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">缓存管理</h3>
                        <p class="text-gray-500 text-sm">已使用 45.2 MB</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>

            <!-- 数据同步 -->
            <div class="setting-item px-4 py-4 flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-cyan-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-sync text-cyan-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">数据同步</h3>
                        <p class="text-gray-500 text-sm">最后同步：刚刚</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
        </div>
    </div>

    <!-- 帮助与支持 -->
    <div class="px-6 py-4">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">帮助与支持</h2>
        <div class="bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-100">
            <!-- 使用教程 -->
            <div class="setting-item px-4 py-4 flex items-center justify-between border-b border-gray-100">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-graduation-cap text-yellow-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">使用教程</h3>
                        <p class="text-gray-500 text-sm">学习如何使用应用</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>

            <!-- 意见反馈 -->
            <div class="setting-item px-4 py-4 flex items-center justify-between border-b border-gray-100">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-comment-dots text-pink-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">意见反馈</h3>
                        <p class="text-gray-500 text-sm">告诉我们您的想法</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>

            <!-- 关于我们 -->
            <div class="setting-item px-4 py-4 flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-info-circle text-teal-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">关于我们</h3>
                        <p class="text-gray-500 text-sm">版本 1.0.0</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
        </div>
    </div>

    <!-- 退出登录 -->
    <div class="px-6 py-4">
        <button class="w-full bg-red-50 text-red-600 py-4 rounded-2xl font-medium border border-red-100">
            退出登录
        </button>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-3">
        <div class="flex justify-around">
            <div class="flex flex-col items-center">
                <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">首页</span>
            </div>
            <div class="flex flex-col items-center">
                <i class="fas fa-heart text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">收藏</span>
            </div>
            <div class="flex flex-col items-center">
                <i class="fas fa-cog text-green-600 text-lg mb-1"></i>
                <span class="text-xs text-green-600 font-medium">设置</span>
            </div>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="h-20"></div>

    <script>
        function toggleSwitch(element) {
            element.classList.toggle('active');
        }
    </script>
</body>
</html>
